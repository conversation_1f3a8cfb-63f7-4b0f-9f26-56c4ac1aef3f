.footer {
  background: #2c3e50;
  color: white;
  position: relative;
  z-index: 50;
  //   margin-top: 0;
  // padding-top: 1rem;

  // Animation optionnelle (désactivée pour éviter les problèmes)
  // transform: translateY(100%);
  // transition: transform 0.4s ease-out;
  // &.visible {
  //   transform: translateY(0);
  // }
}

  .footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    // align-items: center;
    
  }

  .footer-col {
    h3 {
      color: #3498db;
      margin-bottom: 1rem;
    }

    a {
      display: block;
      color: #ecf0f1;
      margin-bottom: 0.5rem;
      text-decoration: none;

      &:hover {
        color: #3498db;
      }
    }
  }

.brand {
  display: flex;
  flex-direction: column; // <= colonne au lieu de ligne
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  // margin-top: 3rem;
}
span{
      color: #3498db;
      // margin-bottom: 1rem;
}


  .social-icons {
    display: flex;
    gap: 1rem;

    a {
      color: white;
      transition: transform 0.3s;

      &:hover {
        transform: translateY(-3px);
      }
    }
  }

  .copyright {
    text-align: center;
    margin-top: -1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
  }
.align-middle{
  color:white;
  &:hover{
    color: #3498db;
  }
}